package io.github.wifi_password_manager.views

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExperimentalMaterial3ExpressiveApi
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import io.github.wifi_password_manager.ui.theme.WiFiPasswordManagerTheme
import org.koin.compose.viewmodel.koinViewModel

@Composable
fun MainView() {
    val viewModel = koinViewModel<MainViewModel>()

    MainView(onEvent = viewModel::onEvent)
}

@OptIn(ExperimentalMaterial3Api::class, ExperimentalMaterial3ExpressiveApi::class)
@Composable
private fun MainView(onEvent: (MainViewModel.Event) -> Unit) {
    Scaffold(topBar = { TopAppBar(title = { Text(text = "Saved WiFi Networks") }) }) { innerPadding
        ->
        PullToRefreshBox(
            modifier = Modifier.padding(innerPadding),
            isRefreshing = false,
            onRefresh = { onEvent(MainViewModel.Event.GetSavedNetworks) },
        ) {
            LazyColumn(modifier = Modifier.fillMaxSize()) {}
        }
    }
}

@PreviewScreenSizes
@Composable
private fun MainViewPreview() {
    WiFiPasswordManagerTheme { MainView(onEvent = {}) }
}
