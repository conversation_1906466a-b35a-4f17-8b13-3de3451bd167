package io.github.wifi_password_manager.views

import androidx.lifecycle.ViewModel
import io.github.wifi_password_manager.services.WifiService

class MainViewModel(private val wifiService: WifiService) : ViewModel() {
    sealed class State {
        data object Loading : State()

        data class Success(val networks: List<String>) : State()

        data class Error(val message: String) : State()
    }

    sealed class Event {
        data object GetSavedNetworks : Event()
    }

    fun onEvent(event: Event) {
        when (event) {
            Event.GetSavedNetworks -> getSavedNetworks()
        }
    }

    private fun getSavedNetworks() {
        wifiService.getPrivilegedConfiguredNetworks()
    }
}
